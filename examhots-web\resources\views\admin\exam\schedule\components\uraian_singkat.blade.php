@forelse ($questions as $index => $q)
    @php
        $isSelected = isset($selectedQuestionIds) && in_array($q->id, $selectedQuestionIds);
    @endphp
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6 {{ $isSelected ? 'ring-2 ring-yellow-500 bg-yellow-50' : '' }}">
        <!-- Header: Tag dan Score -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-2">
                <span class="text-sm font-semibold bg-green-100 text-green-700 px-3 py-1 rounded-full"><PERSON><PERSON><PERSON></span>
                <span class="text-blue-700 bg-blue-100 px-3 py-1 rounded-full text-sm font-medium">
                    Skor: {{ $q->answers->first()?->score ?? '-' }}
                </span>
                @if ($isSelected)
                    <span
                        class="text-sm font-medium bg-green-100 text-green-700 px-3 py-1 rounded-full border border-green-200">
                        <iconify-icon icon="mdi:check-circle" width="16" height="16"
                            class="inline mr-1"></iconify-icon>
                        Dipilih
                    </span>
                @endif
            </div>
            <span class="text-sm text-gray-500">Soal #{{ $loop->iteration }}</span>
        </div>

        <!-- Gambar Soal -->
        @if (!empty($q->img))
            <div class="flex gap-4 mb-4">
                @foreach (explode(',', $q->img) as $img)
                    <img src="{{ asset('storage/uploads/images/question/' . trim($img)) }}" alt="Gambar Soal"
                        class="w-24 h-24 rounded-lg object-cover shadow-md" />
                @endforeach
            </div>
        @endif

        <!-- Pertanyaan -->
        <div class="question-content text-gray-900 text-lg font-semibold mb-4 prose prose-sm max-w-none">
            <div class="question-text">
                {!! $q->question !!}
            </div>
        </div>

        <!-- Jawaban -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-gray-700 mb-3">Kunci Jawaban:</h4>
            @foreach ($q->answers as $answer)
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <div class="answer-content text-gray-800 leading-relaxed prose prose-sm max-w-none">
                        <div class="answer-text">
                            {!! $answer->answer !!}
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@empty
    <div class="text-center py-12">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
        </svg>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Uraian Singkat</h3>
        <p class="text-gray-500">
            Belum ada soal uraian singkat yang tersedia untuk ujian ini.
        </p>
    </div>
@endforelse

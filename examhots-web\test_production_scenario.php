<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Exam;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Test Production Scenario ===\n\n";

// Hapus data test lama
DB::table('exam')->where('classid', 17)->delete();

// Insert data persis seperti production
$productionData = [
    [
        'id' => 91,
        'name' => 'UTS',
        'startdate' => '2025-07-23',
        'enddate' => '2025-07-23',
        'starttime' => '11:43:00',
        'endtime' => '12:43:00',
        'duration' => 60,
        'token' => 'HOB',
        'kkm' => '75',
        'amountquestion' => 3,
        'questionmaterialid' => 1,
        'trials' => 1,
        'classid' => 17,
        'teacher_id' => 2,
        'show_score' => 1,
        'created_at' => '2025-07-23 04:44:01',
        'updated_at' => '2025-07-23 04:44:01'
    ],
    [
        'id' => 92,
        'name' => 'UTS 2',
        'startdate' => '2025-07-23',
        'enddate' => '2025-07-23',
        'starttime' => '11:44:00',
        'endtime' => '12:44:00',
        'duration' => 60,
        'token' => 'FJV',
        'kkm' => '75',
        'amountquestion' => 3,
        'questionmaterialid' => 1,
        'trials' => 1,
        'classid' => 17,
        'teacher_id' => 68,
        'show_score' => 1,
        'created_at' => '2025-07-23 04:44:20',
        'updated_at' => '2025-07-23 04:45:03'
    ],
    // Tambahkan ujian dengan field NULL seperti di production
    [
        'id' => 82,
        'name' => 'Ujian HOTS 3c',
        'startdate' => null,
        'enddate' => null,
        'starttime' => null,
        'endtime' => null,
        'duration' => 90,
        'token' => 'ES3',
        'kkm' => '75',
        'amountquestion' => 5,
        'questionmaterialid' => 1,
        'trials' => 2,
        'classid' => 17,
        'teacher_id' => 2,
        'show_score' => 1,
        'created_at' => '2025-07-22 13:35:51',
        'updated_at' => '2025-07-22 13:37:11'
    ],
    [
        'id' => 83,
        'name' => 'Test fleksibel',
        'startdate' => null,
        'enddate' => null,
        'starttime' => null,
        'endtime' => null,
        'duration' => 30,
        'token' => 'J0Q',
        'kkm' => '75',
        'amountquestion' => 15,
        'questionmaterialid' => 1,
        'trials' => 3,
        'classid' => 17,
        'teacher_id' => 45,
        'show_score' => 1,
        'created_at' => '2025-07-23 03:46:48',
        'updated_at' => '2025-07-23 03:52:18'
    ]
];

foreach ($productionData as $data) {
    DB::table('exam')->insert($data);
}

echo "Data production berhasil diinsert.\n\n";

// Test scenario persis seperti log production
$classId = 17;
$startDate = '2025-07-23';
$endDate = '2025-07-23';
$startTime = '12:08';
$endTime = '13:08';

echo "=== Test Validasi Konflik ===\n";
echo "Class ID: $classId\n";
echo "Date: $startDate to $endDate\n";
echo "Time: $startTime to $endTime\n\n";

// Cek data yang ada
echo "=== Data yang ada di kelas 17 ===\n";
$allExams = DB::table('exam')->where('classid', 17)->get();
foreach ($allExams as $exam) {
    echo "ID: {$exam->id}, Nama: {$exam->name}\n";
    echo "Tanggal: " . ($exam->startdate ?: 'NULL') . " - " . ($exam->enddate ?: 'NULL') . "\n";
    echo "Waktu: " . ($exam->starttime ?: 'NULL') . " - " . ($exam->endtime ?: 'NULL') . "\n\n";
}

// Test validasi
$hasConflict = Exam::hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime);
echo "=== Hasil Validasi ===\n";
echo "Konflik terdeteksi: " . ($hasConflict ? "YA" : "TIDAK") . "\n\n";

// Test manual query untuk debugging
echo "=== Manual Query Debug ===\n";
$baseQuery = DB::table('exam')
    ->where('classid', $classId)
    ->whereNotNull('startdate')
    ->whereNotNull('enddate')
    ->whereNotNull('starttime')
    ->whereNotNull('endtime')
    ->where('startdate', '!=', '')
    ->where('enddate', '!=', '')
    ->where('starttime', '!=', '')
    ->where('endtime', '!=', '');

echo "Base query SQL: " . $baseQuery->toSql() . "\n";
echo "Base query bindings: " . json_encode($baseQuery->getBindings()) . "\n";

$baseResults = $baseQuery->get();
echo "Base results count: " . $baseResults->count() . "\n\n";

foreach ($baseResults as $exam) {
    echo "Found exam: ID {$exam->id}, {$exam->name}, {$exam->startdate} {$exam->starttime} - {$exam->enddate} {$exam->endtime}\n";
}

echo "\n=== Test Selesai ===\n";

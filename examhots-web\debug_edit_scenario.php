<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Exam;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Debug Edit Scenario ===\n\n";

// Scenario dari log: Edit ujian ID 92
$classId = 17;
$startDate = '2025-07-23';
$endDate = '2025-07-23';
$startTime = '11:44:00';
$endTime = '12:44:00';
$excludeExamId = 92;

echo "Scenario: Edit ujian ID 92\n";
echo "Class ID: $classId\n";
echo "Date: $startDate to $endDate\n";
echo "Time: $startTime to $endTime\n";
echo "Exclude Exam ID: $excludeExamId\n\n";

// Cek semua ujian di kelas 17
echo "=== <PERSON><PERSON><PERSON> 17 ===\n";
$allExams = DB::table('exam')->where('classid', 17)->get();
foreach ($allExams as $exam) {
    echo "ID: {$exam->id}, Nama: {$exam->name}\n";
    echo "Tanggal: " . ($exam->startdate ?: 'NULL') . " - " . ($exam->enddate ?: 'NULL') . "\n";
    echo "Waktu: " . ($exam->starttime ?: 'NULL') . " - " . ($exam->endtime ?: 'NULL') . "\n";
    echo "Created: {$exam->created_at}, Updated: {$exam->updated_at}\n\n";
}

// Test query base step by step
echo "=== Debug Query Step by Step ===\n";

// Step 1: Basic class filter
$step1 = DB::table('exam')->where('classid', $classId);
echo "Step 1 - Class filter: " . $step1->count() . " exams\n";

// Step 2: Add NOT NULL filters
$step2 = clone $step1;
$step2 = $step2->whereNotNull('startdate')
    ->whereNotNull('enddate')
    ->whereNotNull('starttime')
    ->whereNotNull('endtime');
echo "Step 2 - NOT NULL filters: " . $step2->count() . " exams\n";

// Step 3: Add != '' filters
$step3 = clone $step2;
$step3 = $step3->where('startdate', '!=', '')
    ->where('enddate', '!=', '')
    ->where('starttime', '!=', '')
    ->where('endtime', '!=', '');
echo "Step 3 - != '' filters: " . $step3->count() . " exams\n";

// Step 4: Add != '0000-00-00' filters
$step4 = clone $step3;
$step4 = $step4->where('startdate', '!=', '0000-00-00')
    ->where('enddate', '!=', '0000-00-00')
    ->where('starttime', '!=', '00:00:00')
    ->where('endtime', '!=', '00:00:00');
echo "Step 4 - != '0000-00-00' filters: " . $step4->count() . " exams\n";

// Step 5: Add exclude filter
$step5 = clone $step4;
$step5 = $step5->where('id', '!=', $excludeExamId);
echo "Step 5 - Exclude ID $excludeExamId: " . $step5->count() . " exams\n";

// Show final results
$finalResults = $step5->get();
echo "\nFinal results:\n";
foreach ($finalResults as $exam) {
    echo "- ID: {$exam->id}, {$exam->name}, {$exam->startdate} {$exam->starttime} - {$exam->enddate} {$exam->endtime}\n";
}

// Test validasi konflik
echo "\n=== Test Validasi Konflik ===\n";
$hasConflict = Exam::hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime, $excludeExamId);
echo "Konflik terdeteksi: " . ($hasConflict ? "YA" : "TIDAK") . "\n";

// Manual check untuk ujian ID 91 (UTS)
echo "\n=== Manual Check Ujian ID 91 ===\n";
$exam91 = DB::table('exam')->where('id', 91)->first();
if ($exam91) {
    echo "Ujian ID 91 ditemukan:\n";
    echo "Nama: {$exam91->name}\n";
    echo "Tanggal: {$exam91->startdate} - {$exam91->enddate}\n";
    echo "Waktu: {$exam91->starttime} - {$exam91->endtime}\n";
    echo "Class ID: {$exam91->classid}\n";
    
    // Check manual overlap
    if ($exam91->classid == $classId && $exam91->startdate == $startDate) {
        $existingStart = \Carbon\Carbon::createFromFormat('H:i:s', $exam91->starttime);
        $existingEnd = \Carbon\Carbon::createFromFormat('H:i:s', $exam91->endtime);
        $newStart = \Carbon\Carbon::createFromFormat('H:i:s', $startTime);
        $newEnd = \Carbon\Carbon::createFromFormat('H:i:s', $endTime);
        
        if ($existingStart && $existingEnd && $newStart && $newEnd) {
            $overlap = ($newStart->lt($existingEnd) && $existingStart->lt($newEnd));
            echo "Manual overlap check: " . ($overlap ? "YA" : "TIDAK") . "\n";
            echo "Logic: ({$newStart->format('H:i')} < {$existingEnd->format('H:i')}) AND ({$existingStart->format('H:i')} < {$newEnd->format('H:i')})\n";
        }
    }
} else {
    echo "Ujian ID 91 tidak ditemukan!\n";
}

echo "\n=== Debug Selesai ===\n";

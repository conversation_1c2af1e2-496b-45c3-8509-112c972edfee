import 'package:flutter/material.dart';
import '../models/exam.dart';
import '../services/api_service.dart';

class ExamProvider with ChangeNotifier {
  List<Exam> _exams = [];
  ExamFormData? _formData;
  Map<String, List<QuestionInfo>> _questionsByMaterial = {};
  bool _isLoading = false;
  String? _errorMessage;

  List<Exam> get exams => _exams;
  ExamFormData? get formData => _formData;
  Map<String, List<QuestionInfo>> get questionsByMaterial =>
      _questionsByMaterial;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading, {bool notify = true}) {
    _isLoading = loading;
    if (notify) {
      notifyListeners();
    }
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError({bool notify = true}) {
    _errorMessage = null;
    if (notify) {
      notifyListeners();
    }
  }

  // Load all exams
  Future<void> loadExams() async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.getExams();

      if (response['success'] == true) {
        final examsList = response['data']['exams'] as List;
        _exams = examsList.map((exam) => Exam.fromJson(exam)).toList();
      } else {
        _setError(response['message'] ?? 'Failed to load exams');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Refresh exams
  Future<void> refresh() async {
    await loadExams();
  }

  // Search exams
  List<Exam> searchExams(String query) {
    if (query.isEmpty) return _exams;

    return _exams.where((exam) {
      return exam.name.toLowerCase().contains(query.toLowerCase()) ||
          exam.questionmaterial?.name.toLowerCase().contains(
                query.toLowerCase(),
              ) ==
              true ||
          exam.classInfo?.name.toLowerCase().contains(query.toLowerCase()) ==
              true ||
          exam.token.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Load form data (classes and question materials)
  Future<bool> loadFormData() async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.getExamFormData();

      if (response['success'] == true) {
        _formData = ExamFormData.fromJson(response['data']);
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to load form data');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load questions by material ID
  Future<bool> loadQuestionsByMaterial(
    int materialId, {
    bool notify = true,
  }) async {
    try {
      _setLoading(true, notify: notify);
      clearError(notify: notify);

      final response = await ApiService.getQuestionsByMaterial(materialId);

      if (response['success'] == true) {
        final questionsData =
            response['data']['questions'] as Map<String, dynamic>;

        // Convert the grouped questions to our format
        Map<String, List<QuestionInfo>> questions = {};
        questionsData.forEach((type, questionsList) {
          questions[type] =
              (questionsList as List)
                  .map((q) => QuestionInfo.fromJson(q))
                  .toList();
        });

        _questionsByMaterial[materialId.toString()] =
            questions.values.expand((list) => list).toList();

        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to load questions');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false, notify: notify);
    }
  }

  // Get questions for a specific material
  List<QuestionInfo> getQuestionsForMaterial(int materialId) {
    return _questionsByMaterial[materialId.toString()] ?? [];
  }

  // Create new exam
  Future<bool> createExam({
    required String name,
    required String startdate,
    required String enddate,
    required String starttime,
    required String endtime,
    required int duration,
    required String kkm,
    required int classid,
    required int questionmaterialid,
    required int trials,
    required List<int> questionIds,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.createExam(
        name: name,
        startdate: startdate,
        enddate: enddate,
        starttime: starttime,
        endtime: endtime,
        duration: duration,
        kkm: kkm,
        classid: classid,
        questionmaterialid: questionmaterialid,
        trials: trials,
        questionIds: questionIds,
      );

      if (response['success'] == true) {
        // Add new exam to the list
        final newExam = Exam.fromJson(response['data']['exam']);
        _exams.insert(0, newExam);
        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to create exam');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update exam
  Future<bool> updateExam({
    required int id,
    required String name,
    required String startdate,
    required String enddate,
    required String starttime,
    required String endtime,
    required int duration,
    required String kkm,
    required int classid,
    required int questionmaterialid,
    required int trials,
    required List<int> questionIds,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateExam(
        id: id,
        name: name,
        startdate: startdate,
        enddate: enddate,
        starttime: starttime,
        endtime: endtime,
        duration: duration,
        kkm: kkm,
        classid: classid,
        questionmaterialid: questionmaterialid,
        trials: trials,
        questionIds: questionIds,
      );

      if (response['success'] == true) {
        // Update exam in the list
        final updatedExam = Exam.fromJson(response['data']['exam']);
        final index = _exams.indexWhere((exam) => exam.id == id);
        if (index != -1) {
          _exams[index] = updatedExam;
          notifyListeners();
        }
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to update exam');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete exam
  Future<bool> deleteExam(int id) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.deleteExam(id);

      if (response['success'] == true) {
        // Remove exam from the list
        _exams.removeWhere((exam) => exam.id == id);
        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to delete exam');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get exam by ID
  Exam? getExamById(int id) {
    try {
      return _exams.firstWhere((exam) => exam.id == id);
    } catch (e) {
      return null;
    }
  }

  // Filter exams by status
  List<Exam> getExamsByStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'berlangsung':
        return _exams.where((exam) => exam.isActive).toList();
      case 'upcoming':
      case 'akan datang':
        return _exams.where((exam) => exam.isUpcoming).toList();
      case 'finished':
      case 'selesai':
        return _exams.where((exam) => exam.isFinished).toList();
      case 'flexible':
      case 'fleksibel':
        return _exams.where((exam) => exam.status == 'Fleksibel').toList();
      default:
        return _exams;
    }
  }

  // Get exams count by status
  Map<String, int> getExamsCountByStatus() {
    return {
      'total': _exams.length,
      'active': _exams.where((exam) => exam.isActive).length,
      'upcoming': _exams.where((exam) => exam.isUpcoming).length,
      'finished': _exams.where((exam) => exam.isFinished).length,
      'flexible': _exams.where((exam) => exam.status == 'Fleksibel').length,
    };
  }
}

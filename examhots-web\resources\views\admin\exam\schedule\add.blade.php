<!-- <PERSON><PERSON>er -->
<div class="flex items-center justify-between p-6 border-b border-gray-100">
    <div class="flex items-center space-x-4">
        <!-- Icon -->
        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <!-- Title and Subtitle -->
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Tambah Jadwal Ujian</h2>
            <p class="text-sm text-gray-500 mt-1">Isi informasi jadwal ujian untuk menambahkan ke sistem</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors" onclick="closeModal()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>
<form id="examForm" action="{{ route('exam.add.post') }}" method="POST" autocomplete="off">
    @csrf
    <!-- Modal Body -->
    <div class="p-6 space-y-6">
        <!-- Error Alert for Schedule Conflict -->
        @error('schedule_conflict')
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Konflik Jadwal Ujian
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p style="white-space: pre-line;">{{ $message }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @enderror

        <!-- Nama Ujian -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Nama Ujian <span class="text-red-500">*</span>
            </label>
            <input type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                id="name" name="name" placeholder="Contoh: Ujian Tengah Semester Matematika" required>
            @error('name')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>

        <!-- Checkbox untuk Tentukan Jadwal Ujian -->
        <div class="mb-4">
            <label class="flex items-center space-x-3">
                <input type="checkbox" id="scheduleExam" name="schedule_exam" value="1"
                    class="w-4 h-4 text-primary-blue bg-gray-100 border-gray-300 rounded focus:ring-primary-blue focus:ring-2">
                <span class="text-sm font-medium text-gray-700">Tentukan Jadwal Ujian</span>
            </label>
            <p class="text-sm text-gray-500 mt-1">Centang untuk menentukan tanggal dan waktu ujian. Jika tidak
                dicentang, durasi ujian akan diinput manual.</p>
        </div>

        <!-- Container untuk Jadwal Ujian -->
        <div id="scheduleContainer" class="hidden space-y-4">
            <!-- Tanggal & Waktu Mulai -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="startdate" class="block text-sm font-medium text-gray-700 mb-2">
                        Tanggal Mulai <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="startdate" name="startdate">
                    @error('startdate')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="starttime" class="block text-sm font-medium text-gray-700 mb-2">
                        Waktu Mulai <span class="text-red-500">*</span>
                    </label>
                    <input type="time"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="starttime" name="starttime">
                    @error('starttime')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Tanggal & Waktu Selesai -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="enddate" class="block text-sm font-medium text-gray-700 mb-2">
                        Tanggal Selesai <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="enddate" name="enddate">
                    @error('enddate')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="endtime" class="block text-sm font-medium text-gray-700 mb-2">
                        Waktu Selesai <span class="text-red-500">*</span>
                    </label>
                    <input type="time"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="endtime" name="endtime">
                    @error('endtime')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Manual Duration Input (shown when schedule is not set) -->
        <div id="manualDurationContainer" class="space-y-4">
            <div>
                <label for="manual_duration" class="block text-sm font-medium text-gray-700 mb-2">
                    Durasi Ujian (Menit) <span class="text-red-500">*</span>
                </label>
                <input type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="manual_duration" name="manual_duration" placeholder="Contoh: 90" min="1">
                <p class="text-sm text-gray-500 mt-1">Masukkan durasi ujian dalam menit</p>
                @error('manual_duration')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>

        <!-- Bank Soal -->
        <div>
            <label for="questionmaterialid" class="block text-sm font-medium text-gray-700 mb-2">
                Bank Soal <span class="text-red-500">*</span>
            </label>
            <select
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                id="questionmaterialid" name="questionmaterialid" required>
                <option value="">Pilih bank soal</option>
                @foreach ($questionMaterial as $question)
                    <option value="{{ $question->id }}">{{ $question->name }}</option>
                @endforeach
            </select>
            @error('questionmaterialid')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" class="hidden">
            <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="animate-spin h-5 w-5 text-primary-blue" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    <span class="text-primary-blue font-medium">Memuat soal...</span>
                </div>
            </div>
        </div>

        <!-- Soal Container -->
        <div id="soal-container" class="hidden">
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Pilih Soal</h4>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        <button
                            class="tab-button active py-2 px-1 border-b-2 border-primary-blue text-primary-blue font-medium text-sm"
                            data-tab="pg">
                            Pilihan Ganda
                        </button>
                        <button
                            class="tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm"
                            data-tab="uraian">
                            Uraian Singkat
                        </button>
                        <button
                            class="tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm"
                            data-tab="esai">
                            Esai
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <div id="pg" class="tab-pane active"></div>
                    <div id="uraian" class="tab-pane hidden"></div>
                    <div id="esai" class="tab-pane hidden"></div>
                </div>

                <div class="text-right mt-4">
                    <span class="text-lg font-semibold text-gray-800">
                        Total Soal Dipilih: <span id="total-soal" class="text-primary-blue">0</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Kelas -->
        <div>
            <label for="classid" class="block text-sm font-medium text-gray-700 mb-2">
                Kelas <span class="text-red-500">*</span>
            </label>
            <select
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                id="classid" name="classid" required>
                <option value="">Pilih kelas</option>
                @foreach ($classStudent as $class)
                    <option value="{{ $class->id }}">{{ $class->name }}</option>
                @endforeach
            </select>
            @error('classid')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>

        <!-- Guru (hanya untuk admin) -->
        @if ($isAdmin)
            <div>
                <label for="teacher_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Guru <span class="text-red-500">*</span>
                </label>
                <select
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="teacher_id" name="teacher_id" required>
                    <option value="">Pilih guru</option>
                    @foreach ($teachers as $teacher)
                        <option value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                    @endforeach
                </select>
                @error('teacher_id')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        @endif

        <!-- Durasi, KKM & Percobaan -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Auto Duration (shown when schedule is set) -->
            <div id="autoDurationContainer" class="hidden">
                <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                    Durasi (Menit) <span class="text-red-500">*</span>
                </label>
                <input type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="duration" name="duration" placeholder="0" readonly>
                <p class="text-xs text-gray-500 mt-1">Durasi akan dihitung otomatis berdasarkan waktu mulai dan selesai
                </p>
                @error('duration')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="kkm" class="block text-sm font-medium text-gray-700 mb-2">
                    KKM <span class="text-red-500">*</span>
                </label>
                <input type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="kkm" name="kkm" value="75" placeholder="75" required>
                @error('kkm')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="trials" class="block text-sm font-medium text-gray-700 mb-2">
                    Jumlah Percobaan <span class="text-red-500">*</span>
                </label>
                <input type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="trials" name="trials" value="1" min="1" required>
                @error('trials')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <div class="flex items-center">
                    <input type="checkbox"
                        class="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                        id="show_score" name="show_score" value="1" checked>
                    <label for="show_score" class="ml-2 block text-sm font-medium text-gray-700">
                        Tampilkan hasil skor ujian kepada siswa
                    </label>
                </div>
                <p class="text-xs text-gray-500 mt-1">Jika dicentang, siswa dapat melihat skor ujian mereka setelah
                    selesai mengerjakan</p>
                @error('show_score')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>
    </div>
</form>
</div>

<!-- Modal Footer -->
<div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-100 bg-gray-50 rounded-b-2xl">
    <button type="button" onclick="closeModal()"
        class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
        Batal
    </button>
    <button type="submit" form="examForm"
        class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center space-x-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>Simpan Jadwal</span>
    </button>
</div>

<script>
    (function() {
        console.log('Modal script loaded');

        const soalContainer = document.getElementById('soal-container');
        const totalSoalSpan = document.getElementById('total-soal');
        const mapelSelect = document.getElementById('questionmaterialid');

        console.log('Elements found:', {
            soalContainer: !!soalContainer,
            totalSoalSpan: !!totalSoalSpan,
            mapelSelect: !!mapelSelect
        });

        if (!mapelSelect) {
            console.error('mapelSelect element not found!');
        } else {
            console.log('Adding event listener to mapelSelect');
        }

        // Add form submit debugging
        const examForm = document.getElementById('examForm');
        if (examForm) {
            console.log('Form found, adding submit listener');
            examForm.addEventListener('submit', function(e) {
                console.log('Form submit triggered');

                // Check if any questions are selected
                const selectedQuestions = document.querySelectorAll('input[name="question_ids[]"]:checked');
                console.log('Selected questions:', selectedQuestions.length);

                if (selectedQuestions.length === 0) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Peringatan!',
                        text: 'Silakan pilih minimal 1 soal untuk ujian!',
                        confirmButtonColor: '#455A9D'
                    });
                    return false;
                }

                // Clear date/time fields if schedule checkbox is not checked
                const scheduleCheckbox = document.getElementById('scheduleExam');
                if (!scheduleCheckbox || !scheduleCheckbox.checked) {
                    document.getElementById('startdate').value = '';
                    document.getElementById('enddate').value = '';
                    document.getElementById('starttime').value = '';
                    document.getElementById('endtime').value = '';
                    document.getElementById('duration').value = '';
                }

                console.log('Form validation passed, submitting...');
            });
        } else {
            console.error('Form examForm not found!');
        }

        if (mapelSelect) {
            const tabMap = {
                'pilihan_ganda': 'pg',
                'uraian_singkat': 'uraian',
                'esai': 'esai'
            };

            // Tab functionality
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('tab-button')) {
                    e.preventDefault();

                    // Remove active class from all tabs
                    document.querySelectorAll('.tab-button').forEach(btn => {
                        btn.classList.remove('active', 'border-primary-blue', 'text-primary-blue');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });

                    // Add active class to clicked tab
                    e.target.classList.add('active', 'border-primary-blue', 'text-primary-blue');
                    e.target.classList.remove('border-transparent', 'text-gray-500');

                    // Hide all tab panes
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.add('hidden');
                        pane.classList.remove('active');
                    });

                    // Show target tab pane
                    const targetTab = e.target.getAttribute('data-tab');
                    const targetPane = document.getElementById(targetTab);
                    if (targetPane) {
                        targetPane.classList.remove('hidden');
                        targetPane.classList.add('active');
                    }
                }
            });

            mapelSelect.addEventListener('change', async function() {
                const matpelId = this.value;
                const loadingIndicator = document.getElementById('loading-indicator');

                console.log('🔥 BANK SOAL CHANGED! ID:', matpelId);
                console.log('Loading indicator element:', !!loadingIndicator);

                if (!matpelId) {
                    soalContainer.classList.add('hidden');
                    loadingIndicator.classList.add('hidden');
                    return;
                }

                try {
                    // Show loading indicator
                    loadingIndicator.classList.remove('hidden');
                    soalContainer.classList.add('hidden');

                    console.log('Loading soal untuk material ID:', matpelId);
                    const res = await fetch(`/get-soal/${matpelId}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                ?.getAttribute(
                                    'content') || ''
                        }
                    });

                    console.log('Response status:', res.status);
                    console.log('Response headers:', res.headers);

                    if (!res.ok) {
                        const errorText = await res.text();
                        console.error('Error response:', errorText);
                        throw new Error(`HTTP error! status: ${res.status} - ${errorText}`);
                    }

                    const data = await res.json();
                    console.log('Data soal loaded:', data);

                    // Hide loading indicator
                    loadingIndicator.classList.add('hidden');

                    // Kosongkan dulu semua tab
                    for (const key in tabMap) {
                        document.getElementById(tabMap[key]).innerHTML = '';
                    }

                    // Check if data is empty
                    if (Object.keys(data).length === 0) {
                        // Show no data message
                        for (const key in tabMap) {
                            document.getElementById(tabMap[key]).innerHTML = `
                            <div class="text-center py-8">
                                <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada soal</h3>
                                <p class="text-gray-500">Bank soal ini belum memiliki soal. Silakan tambahkan soal terlebih dahulu.</p>
                            </div>
                        `;
                        }
                        soalContainer.classList.remove('hidden');
                        updateSoalCount();
                        return;
                    }

                    // Render per tipe soal
                    Object.entries(data).forEach(([type, soals]) => {
                        const containerId = tabMap[type];
                        if (!containerId) return;

                        let html = `
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Pilih</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Pertanyaan</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Jawaban</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">`;

                        soals.forEach((soal) => {
                            let jawabanList =
                                '<em class="text-gray-500">Tidak ada jawaban</em>';

                            if (soal.answers && soal.answers.length > 0) {
                                if (type === 'pilihan_ganda') {
                                    jawabanList =
                                        `<ul class="list-disc list-inside space-y-1">${soal.answers.map((a, i) => `<li class="text-sm">${String.fromCharCode(65 + i)}. ${a.answer}</li>`).join('')}</ul>`;
                                } else {
                                    jawabanList =
                                        `<ul class="list-disc list-inside space-y-1">${soal.answers.map((a) => `<li class="text-sm">${a.answer}</li>`).join('')}</ul>`;
                                }
                            }

                            html += `
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-center">
                                    <input type="checkbox" class="checkbox-soal w-4 h-4 text-primary-blue border-gray-300 rounded focus:ring-primary-blue" name="question_ids[]" value="${soal.id}" checked>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">${soal.question}</td>
                                <td class="px-4 py-3 text-sm text-gray-700">${jawabanList}</td>
                            </tr>`;
                        });

                        html += `</tbody></table></div>`;
                        document.getElementById(containerId).innerHTML = html;
                    });

                    soalContainer.classList.remove('hidden');
                    updateSoalCount();
                } catch (error) {
                    console.error('Error loading soal:', error);
                    loadingIndicator.classList.add('hidden');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal memuat soal. Silakan coba lagi.',
                        confirmButtonColor: '#455A9D'
                    });
                }
            });

            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('checkbox-soal')) {
                    updateSoalCount();
                }
            });

            function updateSoalCount() {
                const checked = document.querySelectorAll('.checkbox-soal:checked');
                totalSoalSpan.textContent = checked.length;
            }

            updateSoalCount();
        }

        // Handle schedule checkbox toggle
        const scheduleCheckbox = document.getElementById('scheduleExam');
        const scheduleContainer = document.getElementById('scheduleContainer');
        const manualDurationContainer = document.getElementById('manualDurationContainer');
        const autoDurationContainer = document.getElementById('autoDurationContainer');

        function toggleScheduleMode() {
            const isScheduled = scheduleCheckbox.checked;

            if (isScheduled) {
                // Show schedule fields, hide manual duration
                scheduleContainer.classList.remove('hidden');
                manualDurationContainer.classList.add('hidden');
                autoDurationContainer.classList.remove('hidden');

                // Make schedule fields required
                document.getElementById('startdate').required = true;
                document.getElementById('enddate').required = true;
                document.getElementById('starttime').required = true;
                document.getElementById('endtime').required = true;
                document.getElementById('duration').required = true;

                // Remove manual duration requirement
                document.getElementById('manual_duration').required = false;
                document.getElementById('manual_duration').value = '';
            } else {
                // Hide schedule fields, show manual duration
                scheduleContainer.classList.add('hidden');
                manualDurationContainer.classList.remove('hidden');
                autoDurationContainer.classList.add('hidden');

                // Remove schedule fields requirement
                document.getElementById('startdate').required = false;
                document.getElementById('enddate').required = false;
                document.getElementById('starttime').required = false;
                document.getElementById('endtime').required = false;
                document.getElementById('duration').required = false;

                // Clear schedule values
                document.getElementById('startdate').value = '';
                document.getElementById('enddate').value = '';
                document.getElementById('starttime').value = '';
                document.getElementById('endtime').value = '';
                document.getElementById('duration').value = '';

                // Make manual duration required
                document.getElementById('manual_duration').required = true;
            }
        }

        // Add event listener for checkbox
        scheduleCheckbox.addEventListener('change', toggleScheduleMode);

        // Initialize the form state
        toggleScheduleMode();

        // Duration auto-calculation
        function calculateDuration() {
            const startDate = document.getElementById('startdate').value;
            const endDate = document.getElementById('enddate').value;
            const startTime = document.getElementById('starttime').value;
            const endTime = document.getElementById('endtime').value;
            const durationInput = document.getElementById('duration');

            if (startDate && endDate && startTime && endTime) {
                // Create datetime objects
                const startDateTime = new Date(`${startDate}T${startTime}`);
                const endDateTime = new Date(`${endDate}T${endTime}`);

                // Calculate difference in minutes
                const diffMs = endDateTime - startDateTime;
                const diffMinutes = Math.floor(diffMs / (1000 * 60));

                if (diffMinutes > 0) {
                    durationInput.value = diffMinutes;
                } else {
                    durationInput.value = 0;
                    if (diffMinutes < 0) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Waktu selesai tidak boleh lebih awal dari waktu mulai!',
                            confirmButtonColor: '#455A9D'
                        });
                    }
                }
            } else {
                durationInput.value = 0;
            }
        }

        // Add event listeners for duration calculation
        document.getElementById('startdate').addEventListener('change', calculateDuration);
        document.getElementById('enddate').addEventListener('change', calculateDuration);
        document.getElementById('starttime').addEventListener('change', calculateDuration);
        document.getElementById('endtime').addEventListener('change', calculateDuration);
    })();
</script>

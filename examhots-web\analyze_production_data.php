<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Exam;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Analisis Data Production ===\n\n";

// Data production yang Anda berikan
$productionData = [
    ['id' => 91, 'name' => 'UTS', 'startdate' => '2025-07-23', 'enddate' => '2025-07-23', 'starttime' => '11:43:00', 'endtime' => '12:43:00', 'classid' => 17],
    ['id' => 92, 'name' => 'UTS 2', 'startdate' => '2025-07-23', 'enddate' => '2025-07-23', 'starttime' => '11:44:00', 'endtime' => '12:44:00', 'classid' => 17],
    ['id' => 95, 'name' => 'TEST', 'startdate' => '2025-07-23', 'enddate' => '2025-07-23', 'starttime' => '12:08:00', 'endtime' => '13:08:00', 'classid' => 17],
];

// Test case yang sama dengan log production
$testClassId = 17;
$testStartDate = '2025-07-23';
$testEndDate = '2025-07-23';
$testStartTime = '12:08';
$testEndTime = '13:08';

echo "Test case dari log production:\n";
echo "Class ID: $testClassId\n";
echo "Date: $testStartDate to $testEndDate\n";
echo "Time: $testStartTime to $testEndTime\n\n";

// Simulasi kondisi production
echo "=== Simulasi Kondisi Production ===\n";

// 1. Test dengan format waktu HH:MM vs HH:MM:SS
echo "1. Test format waktu:\n";
echo "   Input: $testStartTime (HH:MM)\n";
echo "   Database: 11:43:00 (HH:MM:SS)\n";

$timeParseTest1 = \Carbon\Carbon::createFromFormat('H:i', $testStartTime);
$timeParseTest2 = \Carbon\Carbon::createFromFormat('H:i:s', '11:43:00');
echo "   Parse HH:MM: " . ($timeParseTest1 ? "SUCCESS" : "FAILED") . "\n";
echo "   Parse HH:MM:SS: " . ($timeParseTest2 ? "SUCCESS" : "FAILED") . "\n\n";

// 2. Test overlap logic manual
echo "2. Test overlap logic manual:\n";
foreach ($productionData as $exam) {
    if ($exam['classid'] != $testClassId) continue;
    
    echo "   Checking against: {$exam['name']} ({$exam['starttime']} - {$exam['endtime']})\n";
    
    // Parse times
    $existingStart = \Carbon\Carbon::createFromFormat('H:i:s', $exam['starttime']);
    $existingEnd = \Carbon\Carbon::createFromFormat('H:i:s', $exam['endtime']);
    $newStart = \Carbon\Carbon::createFromFormat('H:i', $testStartTime);
    $newEnd = \Carbon\Carbon::createFromFormat('H:i', $testEndTime);
    
    if ($existingStart && $existingEnd && $newStart && $newEnd) {
        // Check time overlap: start1 < end2 AND start2 < end1
        $overlap = ($newStart->lt($existingEnd) && $existingStart->lt($newEnd));
        echo "     Time overlap: " . ($overlap ? "YES" : "NO") . "\n";
        echo "     Logic: ({$newStart->format('H:i')} < {$existingEnd->format('H:i')}) AND ({$existingStart->format('H:i')} < {$newEnd->format('H:i')})\n";
        echo "     Result: (" . ($newStart->lt($existingEnd) ? "true" : "false") . ") AND (" . ($existingStart->lt($newEnd) ? "true" : "false") . ") = " . ($overlap ? "true" : "false") . "\n";
    } else {
        echo "     Time parsing failed!\n";
    }
    echo "\n";
}

// 3. Test query conditions
echo "3. Test query conditions:\n";
foreach ($productionData as $exam) {
    if ($exam['classid'] != $testClassId) continue;
    
    echo "   Exam: {$exam['name']}\n";
    
    // Test date overlap conditions
    $cond1 = ($exam['startdate'] <= $testStartDate && $exam['enddate'] >= $testStartDate);
    $cond2 = ($exam['startdate'] <= $testEndDate && $exam['enddate'] >= $testEndDate);
    $cond3 = ($exam['startdate'] >= $testStartDate && $exam['enddate'] <= $testEndDate);
    $cond4 = ($exam['startdate'] <= $testStartDate && $exam['enddate'] >= $testEndDate);
    
    echo "     Condition 1 (new starts during existing): $cond1\n";
    echo "     Condition 2 (new ends during existing): $cond2\n";
    echo "     Condition 3 (new contains existing): $cond3\n";
    echo "     Condition 4 (existing contains new): $cond4\n";
    echo "     Any condition true: " . ($cond1 || $cond2 || $cond3 || $cond4 ? "YES" : "NO") . "\n\n";
}

echo "=== Kesimpulan ===\n";
echo "Berdasarkan data production, seharusnya ada konflik karena:\n";
echo "- UTS (11:43-12:43) overlap dengan TEST (12:08-13:08)\n";
echo "- UTS 2 (11:44-12:44) overlap dengan TEST (12:08-13:08)\n\n";

echo "Kemungkinan masalah di production:\n";
echo "1. Format waktu input berbeda (HH:MM vs HH:MM:SS)\n";
echo "2. Ada field yang NULL atau kosong\n";
echo "3. Query tidak menemukan data karena kondisi WHERE\n";
echo "4. Timezone atau locale issue\n\n";

echo "=== Analisis Selesai ===\n";

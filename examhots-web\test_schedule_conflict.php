<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Exam;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Test Validasi Konflik Jadwal Ujian ===\n\n";

// Test case 1: Ujian dengan waktu yang sama persis dengan ujian yang ada
echo "Test 1: Ujian dengan waktu yang sama persis dengan ujian yang ada\n";
$conflict1 = Exam::hasScheduleConflict(
    1, // class id
    '2025-07-05', // start date (sama dengan ujian yang ada)
    '2025-07-05', // end date
    '08:40', // start time (sama dengan ujian yang ada)
    '10:50'  // end time (sama dengan ujian yang ada)
);
echo "Hasil: " . ($conflict1 ? "KONFLIK TERDETEKSI ✓" : "TIDAK ADA KONFLIK ✗") . "\n\n";

// Test case 2: Ujian dengan waktu yang overlap dengan ujian yang ada
echo "Test 2: Ujian dengan waktu yang overlap (09:00-11:00 vs 08:40-10:50)\n";
$conflict2 = Exam::hasScheduleConflict(
    1, // class id
    '2025-07-05', // start date (sama dengan ujian yang ada)
    '2025-07-05', // end date
    '09:00', // start time (overlap dengan ujian yang ada)
    '11:00'  // end time (overlap dengan ujian yang ada)
);
echo "Hasil: " . ($conflict2 ? "KONFLIK TERDETEKSI ✓" : "TIDAK ADA KONFLIK ✗") . "\n\n";

// Test case 3: Ujian dengan waktu yang tidak overlap
echo "Test 3: Ujian dengan waktu yang tidak overlap (11:00-13:00 vs 08:00-10:00)\n";
$conflict3 = Exam::hasScheduleConflict(
    1, // class id
    '2024-01-15', // start date
    '2024-01-15', // end date
    '11:00', // start time
    '13:00'  // end time
);
echo "Hasil: " . ($conflict3 ? "KONFLIK TERDETEKSI ✗" : "TIDAK ADA KONFLIK ✓") . "\n\n";

// Test case 4: Ujian di kelas yang berbeda
echo "Test 4: Ujian di kelas yang berbeda (kelas 2 vs kelas 1)\n";
$conflict4 = Exam::hasScheduleConflict(
    2, // class id berbeda
    '2024-01-15', // start date
    '2024-01-15', // end date
    '08:00', // start time
    '10:00'  // end time
);
echo "Hasil: " . ($conflict4 ? "KONFLIK TERDETEKSI ✗" : "TIDAK ADA KONFLIK ✓") . "\n\n";

// Test case 5: Ujian di tanggal yang berbeda
echo "Test 5: Ujian di tanggal yang berbeda\n";
$conflict5 = Exam::hasScheduleConflict(
    1, // class id
    '2024-01-16', // start date berbeda
    '2024-01-16', // end date berbeda
    '08:00', // start time
    '10:00'  // end time
);
echo "Hasil: " . ($conflict5 ? "KONFLIK TERDETEKSI ✗" : "TIDAK ADA KONFLIK ✓") . "\n\n";

// Test case 6: Ujian yang overlap dengan ujian kedua
echo "Test 6: Ujian yang overlap dengan ujian kedua (10:30-11:30 vs 10:55-11:55)\n";
$conflict6 = Exam::hasScheduleConflict(
    1, // class id
    '2025-07-05', // start date (sama dengan ujian yang ada)
    '2025-07-05', // end date
    '10:30', // start time (overlap dengan ujian kedua)
    '11:30'  // end time (overlap dengan ujian kedua)
);
echo "Hasil: " . ($conflict6 ? "KONFLIK TERDETEKSI ✓" : "TIDAK ADA KONFLIK ✗") . "\n\n";

// Test case 7: Ujian yang tepat bersebelahan (tidak overlap)
echo "Test 7: Ujian yang tepat bersebelahan (10:50-10:55 vs 08:40-10:50 dan 10:55-11:55)\n";
$conflict7 = Exam::hasScheduleConflict(
    1, // class id
    '2025-07-05', // start date
    '2025-07-05', // end date
    '10:50', // start time (tepat setelah ujian pertama selesai)
    '10:55'  // end time (tepat sebelum ujian kedua mulai)
);
echo "Hasil: " . ($conflict7 ? "KONFLIK TERDETEKSI ✗" : "TIDAK ADA KONFLIK ✓") . "\n\n";

// Tampilkan ujian yang ada di database untuk referensi
echo "=== Ujian yang ada di database ===\n";
$exams = DB::table('exam')
    ->where('classid', 1)
    ->whereNotNull('startdate')
    ->whereNotNull('starttime')
    ->select('id', 'name', 'startdate', 'enddate', 'starttime', 'endtime', 'classid')
    ->get();

if ($exams->count() > 0) {
    foreach ($exams as $exam) {
        echo "ID: {$exam->id}, Nama: {$exam->name}, Kelas: {$exam->classid}\n";
        echo "Tanggal: {$exam->startdate} - {$exam->enddate}\n";
        echo "Waktu: {$exam->starttime} - {$exam->endtime}\n\n";
    }
} else {
    echo "Tidak ada ujian terjadwal di database.\n";
    echo "Silakan tambahkan ujian terlebih dahulu untuk menguji validasi konflik.\n\n";
}

echo "=== Test Selesai ===\n";

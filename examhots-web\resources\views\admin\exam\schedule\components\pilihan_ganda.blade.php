@forelse ($questions as $index => $q)
    @php
        $isSelected = isset($selectedQuestionIds) && in_array($q->id, $selectedQuestionIds);
    @endphp
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6 {{ $isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '' }}">
        <!-- Header: Tag -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-2">
                <span class="text-sm font-semibold bg-blue-100 text-blue-700 px-3 py-1 rounded-full"><PERSON><PERSON><PERSON></span>
                @if ($isSelected)
                    <span
                        class="text-sm font-medium bg-green-100 text-green-700 px-3 py-1 rounded-full border border-green-200">
                        <iconify-icon icon="mdi:check-circle" width="16" height="16"
                            class="inline mr-1"></iconify-icon>
                        Dipilih
                    </span>
                @endif
            </div>
            <span class="text-sm text-gray-500">Soal #{{ $loop->iteration }}</span>
        </div>

        <!-- Gambar Soal -->
        @if (!empty($q->img))
            <div class="flex gap-4 mb-4">
                @foreach (explode(',', $q->img) as $img)
                    <img src="{{ asset('storage/uploads/images/question/' . trim($img)) }}" alt="Gambar Soal"
                        class="w-24 h-24 rounded-lg object-cover shadow-md" />
                @endforeach
            </div>
        @endif

        <!-- Pertanyaan -->
        <div class="question-content text-gray-900 text-lg font-semibold mb-4 prose prose-sm max-w-none">
            <div class="question-text">
                {!! $q->question !!}
            </div>
        </div>

        <!-- Opsi Jawaban -->
        @php
            $labels = ['A', 'B', 'C', 'D', 'E'];
        @endphp
        <div class="space-y-3">
            @foreach ($q->answers as $key => $answer)
                <div
                    class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200
                    @if ($answer->is_correct) bg-green-50 border-green-200 @endif">
                    <div
                        class="w-8 h-8 rounded-full border-2 flex items-center justify-center
                        @if ($answer->is_correct) border-green-500 bg-green-500 text-white @else border-gray-300 @endif">
                        <span class="text-sm font-medium">{{ $labels[$key] ?? $key + 1 }}</span>
                    </div>
                    <div
                        class="answer-content text-gray-700 prose prose-sm max-w-none @if ($answer->is_correct) font-semibold text-green-700 @endif">
                        <div class="answer-text">
                            {!! $answer->answer !!}
                        </div>
                    </div>
                    @if ($answer->is_correct)
                        <svg class="w-5 h-5 text-green-500 ml-auto" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
@empty
    <div class="text-center py-12">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01">
            </path>
        </svg>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Pilihan Ganda</h3>
        <p class="text-gray-500">
            Belum ada soal pilihan ganda yang tersedia untuk ujian ini.
        </p>
    </div>
@endforelse

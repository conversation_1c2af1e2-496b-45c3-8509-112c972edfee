<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class Exam extends Model
{
    use HasFactory;

    protected $table = 'exam';

    protected $fillable = [
        'name',
        'startdate',
        'enddate',
        'starttime',
        'endtime',
        'duration',
        'token',
        'kkm',
        'amountquestion',
        'questionmaterialid',
        'trials',
        'classid',
        'teacher_id',
        'show_score',
    ];

    public function questionmaterial()
    {
        return $this->belongsTo(QuestionMaterial::class, 'questionmaterialid');
    }

    public function class()
    {
        return $this->belongsTo(StudentClass::class, 'classid');
    }

    public function examDetails()
    {
        return $this->hasMany(ExamDetail::class, 'examid');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Parse time string to Carbon, handling both HH:MM and HH:MM:SS formats
     */
    private static function parseTime($timeString)
    {
        // Return null if timeString is null or empty
        if (empty($timeString) || $timeString === null) {
            return null;
        }

        // Try HH:MM:SS format first
        try {
            return Carbon::createFromFormat('H:i:s', $timeString);
        } catch (\Exception $e) {
            // Fall back to HH:MM format
            try {
                return Carbon::createFromFormat('H:i', $timeString);
            } catch (\Exception $e2) {
                // If both formats fail, return null
                return null;
            }
        }
    }

    /**
     * Check if there's a schedule conflict for a class
     *
     * @param int $classId
     * @param string $startDate
     * @param string $endDate
     * @param string $startTime
     * @param string $endTime
     * @param int|null $excludeExamId - ID ujian yang dikecualikan (untuk edit)
     * @return bool
     */
    public static function hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime, $excludeExamId = null)
    {
        // If any of the new exam's schedule data is empty, no conflict check needed
        if (empty($startDate) || empty($endDate) || empty($startTime) || empty($endTime)) {
            Log::info('Schedule conflict check skipped - empty schedule data', [
                'classId' => $classId,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'startTime' => $startTime,
                'endTime' => $endTime,
                'excludeExamId' => $excludeExamId
            ]);
            return false;
        }

        Log::info('Checking schedule conflict', [
            'classId' => $classId,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'excludeExamId' => $excludeExamId
        ]);

        $query = self::where('classid', $classId)
            // Only check exams that have complete schedule data
            ->whereNotNull('startdate')
            ->whereNotNull('enddate')
            ->whereNotNull('starttime')
            ->whereNotNull('endtime')
            ->where('startdate', '!=', '')
            ->where('enddate', '!=', '')
            ->where('starttime', '!=', '')
            ->where('endtime', '!=', '');

        // Exclude current exam if editing
        if ($excludeExamId) {
            $query->where('id', '!=', $excludeExamId);
        }

        // Check for date overlap
        $conflictingExams = $query->where(function ($q) use ($startDate, $endDate) {
            $q->where(function ($subQ) use ($startDate, $endDate) {
                // Case 1: New exam starts during existing exam
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $startDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 2: New exam ends during existing exam
                $subQ->where('startdate', '<=', $endDate)
                    ->where('enddate', '>=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 3: New exam completely contains existing exam
                $subQ->where('startdate', '>=', $startDate)
                    ->where('enddate', '<=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                // Case 4: Existing exam completely contains new exam
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $endDate);
            });
        })->get();

        // If no date conflicts, no time conflicts either
        if ($conflictingExams->isEmpty()) {
            return false;
        }

        // Check for time conflicts within overlapping dates
        foreach ($conflictingExams as $exam) {
            if (self::hasTimeConflict($exam, $startDate, $endDate, $startTime, $endTime)) {
                Log::info('Schedule conflict detected', [
                    'classId' => $classId,
                    'conflicting_exam' => [
                        'id' => $exam->id,
                        'name' => $exam->name,
                        'startdate' => $exam->startdate,
                        'enddate' => $exam->enddate,
                        'starttime' => $exam->starttime,
                        'endtime' => $exam->endtime
                    ]
                ]);
                return true;
            }
        }

        Log::info('No schedule conflict found', [
            'classId' => $classId,
            'checked_exams_count' => $conflictingExams->count()
        ]);
        return false;
    }

    /**
     * Check if there's a time conflict between two exams
     */
    private static function hasTimeConflict($existingExam, $newStartDate, $newEndDate, $newStartTime, $newEndTime)
    {
        // Skip conflict check if either exam doesn't have complete schedule data
        if (
            empty($existingExam->startdate) || empty($existingExam->enddate) ||
            empty($existingExam->starttime) || empty($existingExam->endtime) ||
            empty($newStartDate) || empty($newEndDate) ||
            empty($newStartTime) || empty($newEndTime)
        ) {
            return false; // No conflict if any exam doesn't have complete schedule
        }

        // Convert times to Carbon for easier comparison
        // Handle both HH:MM and HH:MM:SS formats
        $existingStart = self::parseTime($existingExam->starttime);
        $existingEnd = self::parseTime($existingExam->endtime);
        $newStart = self::parseTime($newStartTime);
        $newEnd = self::parseTime($newEndTime);

        // If any time parsing failed, assume there's a conflict to be safe
        if (!$existingStart || !$existingEnd || !$newStart || !$newEnd) {
            // Log the parsing failure for debugging
            Log::warning('Time parsing failed in conflict check', [
                'existing_start' => $existingExam->starttime,
                'existing_end' => $existingExam->endtime,
                'new_start' => $newStartTime,
                'new_end' => $newEndTime
            ]);
            return true; // Assume conflict to be safe
        }

        // Check if dates overlap
        try {
            $existingStartDate = Carbon::parse($existingExam->startdate);
            $existingEndDate = Carbon::parse($existingExam->enddate);
            $newStartDateCarbon = Carbon::parse($newStartDate);
            $newEndDateCarbon = Carbon::parse($newEndDate);
        } catch (\Exception $e) {
            // If date parsing fails, assume conflict to be safe
            Log::warning('Date parsing failed in conflict check', [
                'existing_start_date' => $existingExam->startdate,
                'existing_end_date' => $existingExam->enddate,
                'new_start_date' => $newStartDate,
                'new_end_date' => $newEndDate,
                'error' => $e->getMessage()
            ]);
            return true; // Assume conflict to be safe
        }

        // Find overlapping date range
        $overlapStart = $existingStartDate->max($newStartDateCarbon);
        $overlapEnd = $existingEndDate->min($newEndDateCarbon);

        // If there's no date overlap, no conflict
        if ($overlapStart->gt($overlapEnd)) {
            return false;
        }

        // Check for time overlap within the overlapping date range
        // Two time ranges overlap if: start1 < end2 AND start2 < end1
        // This handles all cases including:
        // 1. New exam starts during existing exam time
        // 2. New exam ends during existing exam time
        // 3. New exam completely contains existing exam time
        // 4. Existing exam completely contains new exam time

        // For same-day exams, simple time comparison
        if ($overlapStart->isSameDay($overlapEnd)) {
            return ($newStart->lt($existingEnd) && $existingStart->lt($newEnd));
        }

        // For multi-day exams, there's always a conflict if dates overlap
        // because the time ranges will overlap at some point during the overlapping days
        return true;
    }

    /**
     * Get conflicting exams for a class and time period
     */
    public static function getConflictingExams($classId, $startDate, $endDate, $startTime, $endTime, $excludeExamId = null)
    {
        // If any of the schedule data is empty, return empty collection
        if (empty($startDate) || empty($endDate) || empty($startTime) || empty($endTime)) {
            return collect();
        }

        $query = self::with(['questionmaterial', 'class'])
            ->where('classid', $classId)
            // Only check exams that have complete schedule data
            ->whereNotNull('startdate')
            ->whereNotNull('enddate')
            ->whereNotNull('starttime')
            ->whereNotNull('endtime')
            ->where('startdate', '!=', '')
            ->where('enddate', '!=', '')
            ->where('starttime', '!=', '')
            ->where('endtime', '!=', '');

        if ($excludeExamId) {
            $query->where('id', '!=', $excludeExamId);
        }

        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->where(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $startDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $endDate)
                    ->where('enddate', '>=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '>=', $startDate)
                    ->where('enddate', '<=', $endDate);
            })->orWhere(function ($subQ) use ($startDate, $endDate) {
                $subQ->where('startdate', '<=', $startDate)
                    ->where('enddate', '>=', $endDate);
            });
        })->get()->filter(function ($exam) use ($startDate, $endDate, $startTime, $endTime) {
            return self::hasTimeConflict($exam, $startDate, $endDate, $startTime, $endTime);
        });
    }
}

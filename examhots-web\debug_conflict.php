<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Exam;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Debug Validasi Konflik Jadwal ===\n\n";

// Test dengan data yang sama seperti di production
$classId = 17;
$startDate = '2025-07-23';
$endDate = '2025-07-23';
$startTime = '12:08';
$endTime = '13:08';

echo "Testing conflict for:\n";
echo "Class ID: $classId\n";
echo "Date: $startDate to $endDate\n";
echo "Time: $startTime to $endTime\n\n";

// Cek ujian yang ada di kelas 17
echo "=== Ujian yang ada di kelas 17 ===\n";
$existingExams = DB::table('exam')
    ->where('classid', $classId)
    ->whereNotNull('startdate')
    ->whereNotNull('starttime')
    ->select('id', 'name', 'startdate', 'enddate', 'starttime', 'endtime', 'classid')
    ->get();

// Jika tidak ada di kelas 17, cek semua ujian
if ($existingExams->isEmpty()) {
    echo "Tidak ada ujian di kelas 17. Cek semua ujian:\n";
    $allExams = DB::table('exam')
        ->whereNotNull('startdate')
        ->whereNotNull('starttime')
        ->select('id', 'name', 'startdate', 'enddate', 'starttime', 'endtime', 'classid')
        ->get();

    foreach ($allExams as $exam) {
        echo "ID: {$exam->id}, Nama: {$exam->name}, Kelas: {$exam->classid}\n";
        echo "Tanggal: {$exam->startdate} - {$exam->enddate}\n";
        echo "Waktu: {$exam->starttime} - {$exam->endtime}\n\n";
    }
}

foreach ($existingExams as $exam) {
    echo "ID: {$exam->id}, Nama: {$exam->name}\n";
    echo "Tanggal: {$exam->startdate} - {$exam->enddate}\n";
    echo "Waktu: {$exam->starttime} - {$exam->endtime}\n\n";
}

// Test validasi konflik
echo "=== Test Validasi Konflik ===\n";
$hasConflict = Exam::hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime);
echo "Hasil: " . ($hasConflict ? "KONFLIK TERDETEKSI" : "TIDAK ADA KONFLIK") . "\n\n";

// Test manual query untuk melihat apa yang terjadi
echo "=== Manual Query Test ===\n";
$query = DB::table('exam')
    ->where('classid', $classId)
    ->whereNotNull('startdate')
    ->whereNotNull('enddate')
    ->whereNotNull('starttime')
    ->whereNotNull('endtime')
    ->where('startdate', '!=', '')
    ->where('enddate', '!=', '')
    ->where('starttime', '!=', '')
    ->where('endtime', '!=', '');

$baseExams = $query->get();
echo "Base exams found: " . $baseExams->count() . "\n";

// Test date overlap query
$dateOverlapQuery = clone $query;
$dateOverlapExams = $dateOverlapQuery->where(function ($q) use ($startDate, $endDate) {
    $q->where(function ($subQ) use ($startDate, $endDate) {
        // Case 1: New exam starts during existing exam
        $subQ->where('startdate', '<=', $startDate)
            ->where('enddate', '>=', $startDate);
    })->orWhere(function ($subQ) use ($startDate, $endDate) {
        // Case 2: New exam ends during existing exam
        $subQ->where('startdate', '<=', $endDate)
            ->where('enddate', '>=', $endDate);
    })->orWhere(function ($subQ) use ($startDate, $endDate) {
        // Case 3: New exam completely contains existing exam
        $subQ->where('startdate', '>=', $startDate)
            ->where('enddate', '<=', $endDate);
    })->orWhere(function ($subQ) use ($startDate, $endDate) {
        // Case 4: Existing exam completely contains new exam
        $subQ->where('startdate', '<=', $startDate)
            ->where('enddate', '>=', $endDate);
    });
})->get();

echo "Date overlap exams found: " . $dateOverlapExams->count() . "\n";
foreach ($dateOverlapExams as $exam) {
    echo "- ID: {$exam->id}, {$exam->startdate} {$exam->starttime} - {$exam->enddate} {$exam->endtime}\n";
}

// Mari buat data test seperti di production
echo "\n=== Membuat Data Test ===\n";

// Insert data test yang mirip dengan production
DB::table('exam')->insert([
    [
        'id' => 91,
        'name' => 'UTS',
        'startdate' => '2025-07-23',
        'enddate' => '2025-07-23',
        'starttime' => '11:43:00',
        'endtime' => '12:43:00',
        'duration' => 60,
        'token' => 'HOB',
        'kkm' => '75',
        'amountquestion' => 3,
        'questionmaterialid' => 1,
        'trials' => 1,
        'classid' => 17,
        'teacher_id' => 2,
        'show_score' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ],
    [
        'id' => 92,
        'name' => 'UTS 2',
        'startdate' => '2025-07-23',
        'enddate' => '2025-07-23',
        'starttime' => '11:44:00',
        'endtime' => '12:44:00',
        'duration' => 60,
        'token' => 'FJV',
        'kkm' => '75',
        'amountquestion' => 3,
        'questionmaterialid' => 1,
        'trials' => 1,
        'classid' => 17,
        'teacher_id' => 68,
        'show_score' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ]
]);

echo "Data test berhasil dibuat.\n\n";

// Test lagi setelah data dibuat
echo "=== Test Ulang Setelah Data Dibuat ===\n";
$hasConflictAfter = Exam::hasScheduleConflict($classId, $startDate, $endDate, $startTime, $endTime);
echo "Hasil: " . ($hasConflictAfter ? "KONFLIK TERDETEKSI" : "TIDAK ADA KONFLIK") . "\n\n";

echo "=== Debug Selesai ===\n";
